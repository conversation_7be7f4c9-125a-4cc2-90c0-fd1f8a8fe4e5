package services

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"solve-api/config"
	"solve-api/models"
	"solve-api/utils"
	"strings"
	"time"
)

// QwenRequest Qwen-VL API 请求结构体
type QwenRequest struct {
	Model string `json:"model"`
	Input struct {
		Messages []QwenMessage `json:"messages"`
	} `json:"input"`
	Parameters struct {
		ResultFormat string `json:"result_format"`
	} `json:"parameters"`
}

type QwenMessage struct {
	Role    string        `json:"role"`
	Content []QwenContent `json:"content"`
}

type QwenContent struct {
	Type  string `json:"type"`
	Text  string `json:"text,omitempty"`
	Image string `json:"image,omitempty"`
}

// QwenResponse Qwen-VL API 响应结构体
type QwenResponse struct {
	Output struct {
		Choices []struct {
			Message struct {
				Content []struct {
					Text string `json:"text"`
				} `json:"content"`
			} `json:"message"`
		} `json:"choices"`
	} `json:"output"`
	Usage struct {
		InputTokens  int `json:"input_tokens"`
		OutputTokens int `json:"output_tokens"`
		TotalTokens  int `json:"total_tokens"`
	} `json:"usage"`
	RequestID string `json:"request_id"`
}

// QwenService Qwen-VL 服务
type QwenService struct {
	apiKey string
	apiURL string
}

// getQwenPrompt 获取Qwen识别提示词
func getQwenPrompt() string {
	return `你是一个专业的图片文字识别专家，请仔细识别图片中的题目内容。

【任务要求】：
1. 仔细识别图片中的题目类型（单选题/多选题/判断题）
2. 完整准确地提取题目问题内容
3. 准确识别所有选项内容
4. 只负责文字识别，不需要分析答案

【识别要求】：
- 必须准确识别每一个文字
- 必须完整提取题目内容
- 必须准确识别所有选项
- 不要遗漏任何信息
- 不要添加自己的理解或分析

【输出格式】（严格按照此格式，只输出题目内容）：
题目类型：[单选题/多选题/判断题]
问题：[完整的题目内容，必须与图片中完全一致]
选项A：[选项A的完整内容]
选项B：[选项B的完整内容]
选项C：[选项C的完整内容]
选项D：[选项D的完整内容]

【特别注意】：
- 如果是判断题，只需要选项A和选项B
- 冒号后直接跟内容，不要任何额外符号
- 每行一个字段，格式必须严格一致
- 只负责文字识别，不要分析答案
- 确保题目内容完整准确`
}

// NewQwenService 创建 Qwen-VL 服务实例
func NewQwenService() *QwenService {
	return &QwenService{
		apiKey: config.AppConfig.QwenAPI.APIKey,
		apiURL: config.AppConfig.QwenAPI.APIURL,
	}
}

// AnalyzeImage 分析图片并返回结构化数据
func (s *QwenService) AnalyzeImage(imageURL string) (*models.QuestionData, error) {
	// 构建请求
	request := QwenRequest{
		Model: "qwen-vl-plus",
	}

	prompt := getQwenPrompt()

	request.Input.Messages = []QwenMessage{
		{
			Role: "user",
			Content: []QwenContent{
				{
					Type: "text",
					Text: prompt,
				},
				{
					Type:  "image",
					Image: imageURL,
				},
			},
		},
	}

	request.Parameters.ResultFormat = "message"

	// 序列化请求
	requestBody, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("序列化请求失败: %v", err)
	}

	// 发送请求
	req, err := http.NewRequest("POST", s.apiURL, bytes.NewBuffer(requestBody))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	req.Header.Set("Authorization", "Bearer "+s.apiKey)
	req.Header.Set("Content-Type", "application/json; charset=utf-8")
	req.Header.Set("Accept", "application/json; charset=utf-8")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("API 请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(responseBody))
	}

	// 解析响应
	var qwenResp QwenResponse
	if err := json.Unmarshal(responseBody, &qwenResp); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	if len(qwenResp.Output.Choices) == 0 || len(qwenResp.Output.Choices[0].Message.Content) == 0 {
		return nil, fmt.Errorf("未获取到有效响应")
	}

	// 解析结构化文本内容
	rawContent := qwenResp.Output.Choices[0].Message.Content[0].Text
	log.Printf("Qwen-VL 原始响应: %s", rawContent)
	log.Printf("Qwen-VL Token使用量 - 输入: %d, 输出: %d, 总计: %d",
		qwenResp.Usage.InputTokens, qwenResp.Usage.OutputTokens, qwenResp.Usage.TotalTokens)

	questionData, err := parseStructuredText(rawContent)
	if err != nil {
		return nil, fmt.Errorf("解析题目数据失败: %v, 原始内容: %s", err, rawContent)
	}

	return questionData, nil
}

// cleanUTF8String 彻底清理字符串，确保是有效的 UTF-8
// 注意：此函数已被utils.CleanUTF8String替代，保留用于兼容性
func cleanUTF8String(s string) string {
	return utils.CleanUTF8String(s)
}

// parseSimpleFormat 解析新的简洁格式（直接内容，不是键值对）
func parseSimpleFormat(lines []string, questionData *models.QuestionData) (*models.QuestionData, error) {
	if len(lines) < 4 {
		return nil, fmt.Errorf("简洁格式数据不完整，至少需要4行（类型、题目、选项A、选项B），实际只有%d行", len(lines))
	}

	// 第1行：题目类型（移除末尾的分号）
	questionData.Type = strings.TrimSuffix(strings.TrimSuffix(lines[0], "；"), ";")
	// 应用增强版文本规范化工具（包括OCR错误修复）
	questionData.Type = utils.NormalizeTextEnhanced(questionData.Type)

	// 第2行：题目内容
	questionData.Question = lines[1]
	// 应用增强版文本规范化工具（包括OCR错误修复）
	questionData.Question = utils.NormalizeTextEnhanced(questionData.Question)

	// 第3行开始：选项内容（处理 "A: 内容" 格式）
	for i := 2; i < len(lines); i++ {
		line := lines[i]

		// 查找选项标识符（A:, B:, C:, D:）
		if strings.HasPrefix(line, "A:") || strings.HasPrefix(line, "A：") {
			content := strings.TrimSpace(strings.TrimPrefix(strings.TrimPrefix(line, "A:"), "A："))
			content = strings.TrimSuffix(strings.TrimSuffix(content, "；"), ";")
			// 应用增强版文本规范化工具（包括OCR错误修复）
			content = utils.NormalizeTextEnhanced(content)
			questionData.Options["A"] = content
		} else if strings.HasPrefix(line, "B:") || strings.HasPrefix(line, "B：") {
			content := strings.TrimSpace(strings.TrimPrefix(strings.TrimPrefix(line, "B:"), "B："))
			content = strings.TrimSuffix(strings.TrimSuffix(content, "；"), ";")
			// 应用增强版文本规范化工具（包括OCR错误修复）
			content = utils.NormalizeTextEnhanced(content)
			questionData.Options["B"] = content
		} else if strings.HasPrefix(line, "C:") || strings.HasPrefix(line, "C：") {
			content := strings.TrimSpace(strings.TrimPrefix(strings.TrimPrefix(line, "C:"), "C："))
			content = strings.TrimSuffix(strings.TrimSuffix(content, "；"), ";")
			// 应用增强版文本规范化工具（包括OCR错误修复）
			content = utils.NormalizeTextEnhanced(content)
			questionData.Options["C"] = content
		} else if strings.HasPrefix(line, "D:") || strings.HasPrefix(line, "D：") {
			content := strings.TrimSpace(strings.TrimPrefix(strings.TrimPrefix(line, "D:"), "D："))
			content = strings.TrimSuffix(strings.TrimSuffix(content, "；"), ";")
			// 应用增强版文本规范化工具（包括OCR错误修复）
			content = utils.NormalizeTextEnhanced(content)
			questionData.Options["D"] = content
		}
	}

	// 验证必要字段
	if questionData.Type == "" {
		return nil, fmt.Errorf("缺少题目类型")
	}
	if questionData.Question == "" {
		return nil, fmt.Errorf("缺少题目内容")
	}
	if len(questionData.Options) < 2 {
		return nil, fmt.Errorf("选项数量不足，至少需要2个选项，实际解析到%d个选项", len(questionData.Options))
	}

	return questionData, nil
}

// parseStructuredText 解析结构化文本格式
func parseStructuredText(content string) (*models.QuestionData, error) {
	// 使用增强版文本规范化工具（包括OCR错误修复）
	content = utils.NormalizeTextEnhanced(content)
	lines := strings.Split(content, "\n")

	questionData := &models.QuestionData{
		Options: make(map[string]string),
	}

	// 清理所有行
	var cleanLines []string
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line != "" {
			cleanLines = append(cleanLines, line)
		}
	}

	// 检查是否是简洁格式（第一行以分号结尾）
	if len(cleanLines) > 0 && (strings.HasSuffix(cleanLines[0], "；") || strings.HasSuffix(cleanLines[0], ";")) {
		return parseSimpleFormat(cleanLines, questionData)
	}

	// 传统的冒号格式解析
	for _, line := range cleanLines {
		// 查找冒号分隔符
		colonIndex := strings.Index(line, "：")
		if colonIndex == -1 {
			// 尝试英文冒号
			colonIndex = strings.Index(line, ":")
		}
		if colonIndex == -1 {
			continue
		}

		key := strings.TrimSpace(line[:colonIndex])
		value := strings.TrimSpace(line[colonIndex+1:])

		// 移除可能的方括号
		value = strings.Trim(value, "[]")

		// 使用增强版文本规范化工具清理值（包括OCR错误修复）
		value = utils.NormalizeTextEnhanced(value)

		switch key {
		case "题目类型":
			questionData.Type = value
		case "问题":
			questionData.Question = value
		case "选项A":
			questionData.Options["A"] = value
		case "选项B":
			questionData.Options["B"] = value
		case "选项C":
			questionData.Options["C"] = value
		case "选项D":
			questionData.Options["D"] = value
		case "答案":
			questionData.Answer = value
		case "解析":
			questionData.Analysis = value
		}
	}

	// 验证必要字段
	if questionData.Type == "" {
		return nil, fmt.Errorf("缺少题目类型")
	}
	if questionData.Question == "" {
		return nil, fmt.Errorf("缺少问题内容")
	}
	// 注意：简洁格式不包含答案，答案将由DeepSeek提供
	if len(questionData.Options) == 0 {
		return nil, fmt.Errorf("缺少选项")
	}

	return questionData, nil
}

// AnalyzeImageRaw 分析图片并返回原始文本响应（用于传递给DeepSeek）
func (s *QwenService) AnalyzeImageRaw(imageURL string) (string, error) {
	// 构建请求
	request := QwenRequest{
		Model: "qwen-vl-plus",
	}

	prompt := getQwenPrompt()

	request.Input.Messages = []QwenMessage{
		{
			Role: "user",
			Content: []QwenContent{
				{
					Type: "text",
					Text: prompt,
				},
				{
					Type:  "image",
					Image: imageURL,
				},
			},
		},
	}

	request.Parameters.ResultFormat = "message"

	// 序列化请求
	requestBody, err := json.Marshal(request)
	if err != nil {
		return "", fmt.Errorf("序列化请求失败: %v", err)
	}

	// 发送请求
	req, err := http.NewRequest("POST", s.apiURL, bytes.NewBuffer(requestBody))
	if err != nil {
		return "", fmt.Errorf("创建请求失败: %v", err)
	}

	req.Header.Set("Authorization", "Bearer "+s.apiKey)
	req.Header.Set("Content-Type", "application/json; charset=utf-8")
	req.Header.Set("Accept", "application/json; charset=utf-8")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("读取响应失败: %v", err)
	}

	if resp.StatusCode != 200 {
		return "", fmt.Errorf("API 请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(responseBody))
	}

	// 解析响应
	var qwenResp QwenResponse
	if err := json.Unmarshal(responseBody, &qwenResp); err != nil {
		return "", fmt.Errorf("解析响应失败: %v", err)
	}

	if len(qwenResp.Output.Choices) == 0 || len(qwenResp.Output.Choices[0].Message.Content) == 0 {
		return "", fmt.Errorf("未获取到有效响应")
	}

	// 返回原始文本内容
	rawContent := qwenResp.Output.Choices[0].Message.Content[0].Text

	// 1. 记录Qwen返回的原始数据到日志文件
	utils.LogQwenRawResponse(imageURL, rawContent)

	// 控制台日志（保留原有功能）
	log.Printf("Qwen-VL 原始响应: %s", rawContent)
	log.Printf("Qwen-VL Token使用量 - 输入: %d, 输出: %d, 总计: %d",
		qwenResp.Usage.InputTokens, qwenResp.Usage.OutputTokens, qwenResp.Usage.TotalTokens)

	// 🔍 专门为用户打印Qwen原始数据
	fmt.Println("\n" + strings.Repeat("=", 80))
	fmt.Println("🔍 QWEN 原始响应数据 (用户查看)")
	fmt.Println(strings.Repeat("=", 80))
	fmt.Printf("图片URL: %s\n", imageURL)
	fmt.Printf("响应时间: %s\n", time.Now().Format("2006-01-02 15:04:05"))
	fmt.Printf("Token使用: 输入=%d, 输出=%d, 总计=%d\n",
		qwenResp.Usage.InputTokens, qwenResp.Usage.OutputTokens, qwenResp.Usage.TotalTokens)
	fmt.Println("原始响应内容:")
	fmt.Println(rawContent)
	fmt.Println(strings.Repeat("=", 80))
	fmt.Println()

	return rawContent, nil
}
