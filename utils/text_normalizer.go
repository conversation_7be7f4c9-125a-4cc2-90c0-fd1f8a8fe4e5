package utils

import (
	"strings"
	"unicode/utf8"
)

// punctuationMap 全角到半角标点符号映射表
// 使用rune映射确保不会乱码，并保持可控性
var punctuationMap = map[rune]rune{
	// 基础标点符号
	'，': ',',  // 逗号
	'。': '.',  // 句号
	'！': '!',  // 感叹号
	'？': '?',  // 问号
	'：': ':',  // 冒号
	'；': ';',  // 分号
	
	// 引号
	'\u201C': '"',  // 左双引号 (U+201C)
	'\u201D': '"',  // 右双引号 (U+201D)
	'\u2018': '\'', // 左单引号 (U+2018)
	'\u2019': '\'', // 右单引号 (U+2019)
	
	// 括号
	'（': '(',  // 左圆括号
	'）': ')',  // 右圆括号
	'【': '[',  // 左方括号
	'】': ']',  // 右方括号
	'《': '<',  // 左书名号
	'》': '>',  // 右书名号
	'「': '[',  // 左直角引号
	'」': ']',  // 右直角引号
	'『': '[',  // 左双直角引号
	'』': ']',  // 右双直角引号
	
	// 其他符号
	'、': ',',  // 顿号转逗号
	'～': '~',  // 波浪号
	'—': '-',  // 破折号
	'–': '-',  // 短破折号
	'…': '.', // 省略号转句号
	
	// 空格
	'　': ' ',  // 全角空格转半角空格
}

// NormalizePunctuation 标准化标点符号
// 将全角标点符号转换为半角标点符号，确保不会乱码
func NormalizePunctuation(text string) string {
	if text == "" {
		return text
	}
	
	var builder strings.Builder
	builder.Grow(len(text)) // 预分配容量提高性能
	
	for _, ch := range text {
		if mapped, ok := punctuationMap[ch]; ok {
			builder.WriteRune(mapped)
		} else {
			builder.WriteRune(ch)
		}
	}
	
	return builder.String()
}

// CleanUTF8String 清理UTF-8字符串，移除无效字符
func CleanUTF8String(s string) string {
	// 移除BOM标记
	s = strings.TrimPrefix(s, "\uFEFF")
	
	// 移除替换字符（通常表示无法解码的字符）
	s = strings.ReplaceAll(s, "\ufffd", "")
	
	// 确保字符串是有效的UTF-8
	if !utf8.ValidString(s) {
		s = strings.ToValidUTF8(s, "")
	}
	
	return s
}

// NormalizeText 综合文本规范化
// 包括UTF-8清理、标点符号规范化、空格处理
func NormalizeText(text string) string {
	if text == "" {
		return text
	}
	
	// 第一步：清理UTF-8字符
	text = CleanUTF8String(text)
	
	// 第二步：标准化标点符号
	text = NormalizePunctuation(text)
	
	// 第三步：清理空格
	text = NormalizeSpaces(text)
	
	return text
}

// NormalizeSpaces 规范化空格
// 移除多余空格，统一换行符
func NormalizeSpaces(text string) string {
	if text == "" {
		return text
	}
	
	// 统一换行符
	text = strings.ReplaceAll(text, "\r\n", "\n")
	text = strings.ReplaceAll(text, "\r", "\n")
	
	// 移除行首行尾空格，但保留换行结构
	lines := strings.Split(text, "\n")
	var cleanedLines []string
	
	for _, line := range lines {
		// 清理每行的前后空格
		line = strings.TrimSpace(line)
		if line != "" {
			// 清理行内多余的空格（将多个连续空格替换为单个空格）
			line = strings.Join(strings.Fields(line), " ")
			cleanedLines = append(cleanedLines, line)
		}
	}
	
	return strings.Join(cleanedLines, "\n")
}

// NormalizeForCache 为缓存生成规范化文本
// 更激进的规范化，用于生成一致的缓存键
func NormalizeForCache(text string) string {
	if text == "" {
		return text
	}
	
	// 基础规范化
	text = NormalizeText(text)
	
	// 转换为小写（如果需要忽略大小写）
	// text = strings.ToLower(text)
	
	// 移除所有空格和换行符（用于缓存键生成）
	text = strings.ReplaceAll(text, " ", "")
	text = strings.ReplaceAll(text, "\n", "")
	text = strings.ReplaceAll(text, "\t", "")
	
	return text
}

// NormalizeOptions 规范化选项内容
func NormalizeOptions(options map[string]string) map[string]string {
	if len(options) == 0 {
		return options
	}

	normalized := make(map[string]string)
	for key, value := range options {
		normalized[key] = NormalizeText(value)
	}

	return normalized
}

// FixOCRErrors 修复常见的OCR识别错误
func FixOCRErrors(text string) string {
	if text == "" {
		return text
	}

	// 修复常见的OCR错误模式
	replacements := map[string]string{
		// 修复数字和百分号之间的空格
		" % ": "%",
		" %": "%",
		"% ": "%",

		// 修复金额表达中的空格
		" 元 ": "元",
		" 元": "元",
		"元 ": "元",

		// 修复"以上"、"以下"等词汇中的空格
		" 以 上": "以上",
		" 以上": "以上",
		"以 上": "以上",
		" 以 下": "以下",
		" 以下": "以下",
		"以 下": "以下",

		// 修复"未达"等词汇中的空格
		" 未 达": "未达",
		" 未达": "未达",
		"未 达": "未达",

		// 修复句号和其他标点前的空格
		" .": ".",
		" ,": ",",
		" ，": ",",
		" 。": ".",

		// 修复连续的空格和标点
		". .": "..",
		", ,": ",,",
		"  ": " ", // 多个空格合并为一个
	}

	// 应用所有替换
	for old, new := range replacements {
		text = strings.ReplaceAll(text, old, new)
	}

	// 再次清理多余空格
	text = strings.Join(strings.Fields(text), " ")

	return text
}

// NormalizeTextEnhanced 增强版文本规范化
// 包括OCR错误修复
func NormalizeTextEnhanced(text string) string {
	if text == "" {
		return text
	}

	// 第一步：基础规范化
	text = NormalizeText(text)

	// 第二步：修复OCR错误
	text = FixOCRErrors(text)

	// 第三步：再次清理空格（因为OCR修复可能产生新的空格问题）
	text = NormalizeSpaces(text)

	return text
}
